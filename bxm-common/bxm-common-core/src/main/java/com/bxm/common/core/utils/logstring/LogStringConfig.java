package com.bxm.common.core.utils.logstring;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 日志字符串配置类
 * 用于配置日志字符串生成的各种选项
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
public class LogStringConfig {

    /** 输出格式 */
    private OutputFormat format = OutputFormat.JSON_LIKE;

    /** 最大递归深度 */
    private int maxDepth = 3;

    /** 最大输出长度 */
    private int maxLength = 1000;

    /** 是否启用缓存 */
    private boolean enableCaching = true;

    /** 是否掩码敏感字段 */
    private boolean maskSensitiveFields = true;

    /** 敏感字段名称集合 */
    private Set<String> sensitiveFieldNames = getDefaultSensitiveFields();

    /** 字段分隔符 */
    private String separator = ", ";

    /** 键值分隔符 */
    private String keyValueSeparator = ": ";

    /** 是否将 boolean 转换为中文 */
    private boolean chineseBooleanFormat = true;

    /** true 的中文表示 */
    private String trueText = "是";

    /** false 的中文表示 */
    private String falseText = "否";

    /** 是否启用忽略字段功能 */
    private boolean enableIgnoreFields = false;

    /** 需要忽略的字段名称集合 */
    private Set<String> ignoreFieldNames = new HashSet<>();

    /** 是否启用字段覆盖功能 */
    private boolean enableFieldOverrides = false;

    /** 字段覆盖映射 */
    private Map<String, String> fieldOverrides = new HashMap<>();

    /** 是否跳过空对象（所有字段都为null或空字符串时） */
    private boolean skipEmptyObjects = true;

    /** 类特定字段忽略映射，key为类名，value为该类需要忽略的字段名集合 */
    private Map<String, Set<String>> classSpecificIgnoreFields = getDefaultClassSpecificIgnoreFields();

    /**
     * 获取默认的敏感字段名称
     */
    private static Set<String> getDefaultSensitiveFields() {
        Set<String> defaultFields = new HashSet<>();
        // 密码相关
        defaultFields.add("password");
        defaultFields.add("pwd");
        defaultFields.add("secret");
        return defaultFields;
    }

    /**
     * 获取默认的类特定字段忽略配置
     */
    private static Map<String, Set<String>> getDefaultClassSpecificIgnoreFields() {
        Map<String, Set<String>> defaultClassIgnoreFields = new HashMap<>();
        return defaultClassIgnoreFields;
    }

    /**
     * 建造者模式
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private LogStringConfig config = new LogStringConfig();

        public Builder format(OutputFormat format) {
            config.format = format;
            return this;
        }

        public Builder maxDepth(int maxDepth) {
            config.maxDepth = maxDepth;
            return this;
        }

        public Builder maxLength(int maxLength) {
            config.maxLength = maxLength;
            return this;
        }

        public Builder enableCaching(boolean enableCaching) {
            config.enableCaching = enableCaching;
            return this;
        }

        public Builder maskSensitiveFields(boolean maskSensitiveFields) {
            config.maskSensitiveFields = maskSensitiveFields;
            return this;
        }

        public Builder sensitiveFieldNames(Set<String> sensitiveFieldNames) {
            config.sensitiveFieldNames = new HashSet<>(sensitiveFieldNames);
            return this;
        }

        public Builder separator(String separator) {
            config.separator = separator;
            return this;
        }

        public Builder keyValueSeparator(String keyValueSeparator) {
            config.keyValueSeparator = keyValueSeparator;
            return this;
        }

        public Builder chineseBooleanFormat(boolean chineseBooleanFormat) {
            config.chineseBooleanFormat = chineseBooleanFormat;
            return this;
        }

        public Builder trueText(String trueText) {
            config.trueText = trueText;
            return this;
        }

        public Builder falseText(String falseText) {
            config.falseText = falseText;
            return this;
        }

        public Builder enableIgnoreFields(boolean enableIgnoreFields) {
            config.enableIgnoreFields = enableIgnoreFields;
            return this;
        }

        public Builder ignoreFieldNames(Set<String> ignoreFieldNames) {
            config.ignoreFieldNames = new HashSet<>(ignoreFieldNames);
            return this;
        }

        public Builder enableFieldOverrides(boolean enableFieldOverrides) {
            config.enableFieldOverrides = enableFieldOverrides;
            return this;
        }

        public Builder fieldOverrides(Map<String, String> fieldOverrides) {
            config.fieldOverrides = new HashMap<>(fieldOverrides);
            return this;
        }

        public Builder skipEmptyObjects(boolean skipEmptyObjects) {
            config.skipEmptyObjects = skipEmptyObjects;
            return this;
        }

        public Builder classSpecificIgnoreFields(Map<String, Set<String>> classSpecificIgnoreFields) {
            // 合并默认配置和用户配置
            Map<String, Set<String>> mergedFields = new HashMap<>(getDefaultClassSpecificIgnoreFields());
            if (classSpecificIgnoreFields != null) {
                mergedFields.putAll(classSpecificIgnoreFields);
            }
            config.classSpecificIgnoreFields = mergedFields;
            return this;
        }

        public LogStringConfig build() {
            return config;
        }
    }

    // Getters and Setters
    public OutputFormat getFormat() {
        return format;
    }

    public void setFormat(OutputFormat format) {
        this.format = format;
    }

    public int getMaxDepth() {
        return maxDepth;
    }

    public void setMaxDepth(int maxDepth) {
        this.maxDepth = maxDepth;
    }

    public int getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    public boolean isEnableCaching() {
        return enableCaching;
    }

    public void setEnableCaching(boolean enableCaching) {
        this.enableCaching = enableCaching;
    }

    public boolean isMaskSensitiveFields() {
        return maskSensitiveFields;
    }

    public void setMaskSensitiveFields(boolean maskSensitiveFields) {
        this.maskSensitiveFields = maskSensitiveFields;
    }

    public Set<String> getSensitiveFieldNames() {
        return sensitiveFieldNames;
    }

    public void setSensitiveFieldNames(Set<String> sensitiveFieldNames) {
        this.sensitiveFieldNames = sensitiveFieldNames;
    }

    public String getSeparator() {
        return separator;
    }

    public void setSeparator(String separator) {
        this.separator = separator;
    }

    public String getKeyValueSeparator() {
        return keyValueSeparator;
    }

    public void setKeyValueSeparator(String keyValueSeparator) {
        this.keyValueSeparator = keyValueSeparator;
    }

    public boolean isChineseBooleanFormat() {
        return chineseBooleanFormat;
    }

    public void setChineseBooleanFormat(boolean chineseBooleanFormat) {
        this.chineseBooleanFormat = chineseBooleanFormat;
    }

    public String getTrueText() {
        return trueText;
    }

    public void setTrueText(String trueText) {
        this.trueText = trueText;
    }

    public String getFalseText() {
        return falseText;
    }

    public void setFalseText(String falseText) {
        this.falseText = falseText;
    }

    public boolean isEnableIgnoreFields() {
        return enableIgnoreFields;
    }

    public void setEnableIgnoreFields(boolean enableIgnoreFields) {
        this.enableIgnoreFields = enableIgnoreFields;
    }

    public Set<String> getIgnoreFieldNames() {
        return ignoreFieldNames;
    }

    public void setIgnoreFieldNames(Set<String> ignoreFieldNames) {
        this.ignoreFieldNames = ignoreFieldNames;
    }

    public boolean isEnableFieldOverrides() {
        return enableFieldOverrides;
    }

    public void setEnableFieldOverrides(boolean enableFieldOverrides) {
        this.enableFieldOverrides = enableFieldOverrides;
    }

    public Map<String, String> getFieldOverrides() {
        return fieldOverrides;
    }

    public void setFieldOverrides(Map<String, String> fieldOverrides) {
        this.fieldOverrides = fieldOverrides;
    }

    public boolean isSkipEmptyObjects() {
        return skipEmptyObjects;
    }

    public void setSkipEmptyObjects(boolean skipEmptyObjects) {
        this.skipEmptyObjects = skipEmptyObjects;
    }

    public Map<String, Set<String>> getClassSpecificIgnoreFields() {
        return classSpecificIgnoreFields;
    }

    public void setClassSpecificIgnoreFields(Map<String, Set<String>> classSpecificIgnoreFields) {
        this.classSpecificIgnoreFields = classSpecificIgnoreFields;
    }
}
