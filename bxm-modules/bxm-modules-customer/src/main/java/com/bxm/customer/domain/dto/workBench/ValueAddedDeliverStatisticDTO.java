package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class ValueAddedDeliverStatisticDTO {

    @ApiModelProperty("待分派数量")
    private Long waitDispatchCount;

    public ValueAddedDeliverStatisticDTO() {
        this.waitDispatchCount = 0L;
    }
}
