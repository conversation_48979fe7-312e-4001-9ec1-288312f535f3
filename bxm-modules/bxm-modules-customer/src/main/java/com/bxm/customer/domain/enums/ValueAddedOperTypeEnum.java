package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 增值交付单操作类型枚举
 * 
 * 用于StatusChangeRequestDTO中的operTypeName字段
 * 前端传递code，后端记录操作日志时使用description
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Getter
@AllArgsConstructor
public enum ValueAddedOperTypeEnum {

    /**
     * 处理（扣款）异常
     */
    EXCEPTION_DEDUCTIONS("exceptionDeductions", "处理（扣款）异常"),

    /**
     * 确认
     */
    CHECK("check", "确认"),

    /**
     * 驳回
     */
    REJECT("reject", "驳回"),

    /**
     * 关闭交付
     */
    CLOSE_DELIVER("closeDeliver", "关闭交付"),

    /**
     * 关闭扣款
     */
    CHECK_DEDUCTIONS("checkDeductions", "关闭扣款"),

    /**
     * 退回
     */
    BACK("back", "退回"),

    /**
     * 分派
     */
    SUBMIT("submit", "分派"),

    /**
     * 交付
     */
    DELIVER("deliver", "交付"),

    /**
     * 补充交付附件
     */
    SUPPLY("supply", "补充交付附件"),

    /**
     * 扣款
     */
    DEDUCTION("deduction", "扣款"),

    /**
     * 处理（交付）异常
     */
    HANDLE_EXCEPTION_DELIVER("handleExceptionDeliver", "处理（交付）异常");

    /**
     * 操作代码（前端传递的值）
     */
    private final String code;

    /**
     * 操作描述（记录日志时使用的中文描述）
     */
    private final String description;

    /**
     * 根据操作代码获取枚举
     *
     * @param code 操作代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ValueAddedOperTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ValueAddedOperTypeEnum operType : values()) {
            if (operType.getCode().equals(code.trim())) {
                return operType;
            }
        }
        return null;
    }

    /**
     * 验证操作代码是否有效
     *
     * @param code 操作代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 根据操作代码获取操作描述
     *
     * @param code 操作代码
     * @return 操作描述，如果代码无效则返回原始代码
     */
    public static String getDescriptionByCode(String code) {
        ValueAddedOperTypeEnum operType = getByCode(code);
        return operType != null ? operType.getDescription() : code;
    }

    /**
     * 判断是否为异常处理操作
     *
     * @return 是否为异常处理操作
     */
    public boolean isExceptionOperation() {
        return this == EXCEPTION_DEDUCTIONS || this == HANDLE_EXCEPTION_DELIVER;
    }

    /**
     * 判断是否为关闭操作
     *
     * @return 是否为关闭操作
     */
    public boolean isCloseOperation() {
        return this == CLOSE_DELIVER || this == CHECK_DEDUCTIONS;
    }

    /**
     * 判断是否为审核类操作
     *
     * @return 是否为审核类操作
     */
    public boolean isReviewOperation() {
        return this == CHECK || this == REJECT || this == BACK;
    }

}