package com.bxm.customer.domain.vo.valueAdded;

import com.bxm.file.api.domain.ValueAddedFileDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 增值交付单查询响应VO
 *
 * 用于getDeliveryOrder接口的响应，包含交付单基本信息和关联的扩展数据
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("增值交付单查询响应VO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeliveryOrderVO {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 交付单编号 */
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /** 交付单标题 */
    @ApiModelProperty(value = "交付单标题")
    private String title;

    /** 客户ID */
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 客户企业名称 */
    @ApiModelProperty(value = "客户企业名称")
    private String customerName;

    /** 统一社会信用代码 */
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /** 税号 */
    @ApiModelProperty(value = "税号")
    private String taxNo;

    /** 纳税性质，1-小规模纳税人，2-一般纳税人 */
    @ApiModelProperty(value = "纳税性质：1-小规模纳税人，2-一般纳税人")
    private Integer taxpayerType;

    /** 纳税性质描述 */
    @ApiModelProperty(value = "纳税性质描述")
    private String taxpayerTypeName;

    /** 增值事项 */
    @ApiModelProperty(value = "增值事项ID")
    private Integer valueAddedItemTypeId;

    /** 增值事项类型对象 */
    @ApiModelProperty(value = "增值事项类型完整对象")
    private ValueAddedItemTypeVO valueAddedItemType;

    /** 账期开始时间 (格式：YYYYMM，如：202301) */
    @ApiModelProperty(value = "账期开始时间，格式YYYYMM")
    private Integer accountingPeriodStart;

    /** 账期结束时间 (格式：YYYYMM，如：202310) */
    @ApiModelProperty(value = "账期结束时间，格式YYYYMM")
    private Integer accountingPeriodEnd;

    /** 联络人手机号 */
    @ApiModelProperty(value = "联络人手机号")
    private String contactMobile;

    /** 联络人证件号 */
    @ApiModelProperty(value = "联络人证件号")
    private String contactIdNumber;

    /** 是否同步汇算，0-否，1-是 */
    @ApiModelProperty(value = "是否同步汇算：0-否，1-是")
    private Boolean syncHandlingFee;

    /** 账务类型信息 */
    @ApiModelProperty(value = "账务类型信息")
    private AccountingInfoVO accountingInfo;

    /** 交付要求 */
    @ApiModelProperty(value = "交付要求")
    private String requirements;

    /** 税负要求 */
    @ApiModelProperty(value = "税负要求")
    private String taxRequirement;

    /** 是否同步改派，0-否，1-是 */
    @ApiModelProperty(value = "是否同步改派：0-否，1-是")
    private Boolean syncReassignment;

    /** 是否同步联络员，0-否，1-是 */
    @ApiModelProperty(value = "是否同步联络员：0-否，1-是")
    private Boolean syncContactPerson;

    /** 是否同步改账，0-否，1-是 */
    @ApiModelProperty(value = "是否同步改账：0-否，1-是")
    private Boolean syncAccountChange;

    /** 是否修改工期，0-否，1-是 */
    @ApiModelProperty(value = "是否修改工期：0-否，1-是")
    private Boolean modifyDueDate;

    /** 交付截止日期 */
    @ApiModelProperty(value = "交付截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ddl;

    /** 发起部门ID */
    @ApiModelProperty(value = "发起部门ID")
    private Long initiateDeptId;

    /** 发起部门信息 */
    @ApiModelProperty(value = "发起部门信息")
    private String initiateDeptInfo;

    /** 发起部门路径 */
    @ApiModelProperty(value = "发起部门路径")
    private List<Long> initiateDeptPath;

    /** 会计部门ID */
    @ApiModelProperty(value = "会计部门ID")
    private Long accountingDeptId;

    /** 会计部门信息 */
    @ApiModelProperty(value = "会计部门信息")
    private String accountingDeptInfo;

    /** 交付状态 */
    @ApiModelProperty(value = "交付状态")
    private String status;

    /** 交付状态描述 */
    @ApiModelProperty(value = "交付状态描述")
    private String statusName;

    /** 业务部门id */
    @ApiModelProperty(value = "业务部门ID")
    private Long businessDeptId;

    /** 顶级业务部门id */
    @ApiModelProperty(value = "顶级业务部门ID")
    private Long businessTopDeptId;

    /** 增值事项名称 */
    @ApiModelProperty(value = "增值事项名称")
    private String itemName;

    /** 总扣缴额 */
    @ApiModelProperty(value = "总扣缴额")
    private BigDecimal totalWithholdingAmount;

    /** 创建人ID */
    @ApiModelProperty(value = "创建人ID")
    private Long createUid;

    // ========== 扩展字段 ==========

    /** 国税账号对象，来自c_value_added_employee，bizType为3-国税账号 */
    @ApiModelProperty(value = "国税账号对象")
    private NationalTaxAccountVO nationalTaxAccount;

    /** 个税账号对象，来自c_value_added_employee，bizType为4-个税账号 */
    @ApiModelProperty(value = "个税账号对象")
    private PersonalTaxAccountVO personalTaxAccount;

    /** 员工信息列表，当itemCode为TAX_SOCIAL_INSURANCE时bizType对应1，TAX_PERSONAL_SALARY对应bizType为2，最多查50条 */
    @ApiModelProperty(value = "员工信息列表，最多50条")
    private List<EmployeeInfo> employeeInfoList;

    /** 交付文件列表，c_value_added_file fileType为1 */
    @ApiModelProperty(value = "交付文件对象列表")
    private List<ValueAddedFileDTO> deliveryFiles;

    /** 交付附件列表，c_value_added_file fileType为6 */
    @ApiModelProperty(value = "交付附件对象列表")
    private List<ValueAddedFileDTO> deliveryAttachments;

    /** 标准附件列表，c_value_added_file fileType为5 */
    @ApiModelProperty(value = "标准附件对象列表")
    private List<ValueAddedFileDTO> standardAttachments;

    /** 操作备注 */
    @ApiModelProperty(value = "操作备注")
    private String remark;

    /** 交付备注 */
    @ApiModelProperty(value = "交付备注")
    private String deliveryRemark;

    /** 发起时间 */
    @ApiModelProperty(value = "发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date initiateTime;

    /** 会计信息名称 */
    @ApiModelProperty(value = "会计信息名称")
    private String accountingNameInfo;

    /** 交付文件数量 */
    @ApiModelProperty(value = "交付文件数量")
    private Integer deliveryFileCnt;

    @ApiModelProperty("服务会计")
    private String customerServiceAccountingInfo;

    @ApiModelProperty("服务顾问")
    private String customerServiceAdvisorInfo;
}
