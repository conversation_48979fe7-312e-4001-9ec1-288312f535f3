package com.bxm.customer.domain.dto;

import com.bxm.file.api.domain.ValueAddedFileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import com.bxm.customer.validation.ValidOperType;
import java.math.BigDecimal;
import java.util.List;

/**
 * 状态变更请求DTO
 *
 * 用于增值交付单状态变更的请求参数封装
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("状态变更请求DTO")
public class StatusChangeRequestDTO {

    /**
     * 交付单编号
     */
    @NotBlank(message = "交付单编号不能为空")
    @Size(max = 50, message = "交付单编号长度不能超过50个字符")
    @ApiModelProperty(value = "交付单编号", required = true, example = "VAD2508051430001A1C")
    private String deliveryOrderNo;

    /**
     * 目标状态（必填）
     */
    @NotBlank(message = "目标状态不能为空")
    @Size(max = 50, message = "目标状态长度不能超过50个字符")
    @ApiModelProperty(value = "目标状态", required = true, example = "SAVED_PENDING_SUBMIT")
    private String targetStatus;

    /**
     * 操作备注
     */
    @Size(max = 1000, message = "操作备注长度不能超过1000个字符")
    @ApiModelProperty(value = "操作备注", example = "系统自动状态变更")
    private String remark;

    /**
     * 交付备注
     */
    @Size(max = 200, message = "交付备注长度不能超过200个字符")
    @ApiModelProperty(value = "交付备注", example = "交付相关说明")
    private String deliveryRemark;

    /** 顶级业务部门id */
    @ApiModelProperty(value = "集团id")
    private Long businessTopDeptId;

    /** 统一社会信用代码 */
    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String creditCode;

    /**
     * 总扣缴额（可选）
     */
    @DecimalMin(value = "0.00", message = "总扣缴额不能为负数")
    @ApiModelProperty(value = "总扣缴额", example = "1000.00", notes = "可选字段，用于扣款相关状态")
    private BigDecimal totalWithholdingAmount;

    /** 交付材料列表，c_value_added_file fileType为1 */
    @ApiModelProperty(value = "交付材料对象列表")
    private List<ValueAddedFileDTO> deliveryFiles;

    /** 标准附件列表，c_value_added_file fileType为5 */
    @ApiModelProperty(value = "标准附件对象列表")
    private List<ValueAddedFileDTO> standardAttachments;

    /** 交付附件列表，c_value_added_file fileType为6 */
    @ApiModelProperty(value = "交付附件对象列表")
    private List<ValueAddedFileDTO> deliveryAttachments;

    /** 操作附件列表，仅在操作日志中记录，不存储到valueAddedFile表 */
    @ApiModelProperty(value = "操作附件对象列表")
    private List<ValueAddedFileDTO> operationAttachments;

    /**
     * 操作类型名称
     * 前端传递操作类型代码，如：check、reject、deliver等
     */
    @ValidOperType(message = "无效的操作类型")
    @Size(max = 100, message = "操作类型名称长度不能超过100个字符")
    @ApiModelProperty(value = "操作类型名称", example = "check", notes = "前端传递操作类型代码，后端记录日志时转换为中文描述")
    private String operTypeName;
}
