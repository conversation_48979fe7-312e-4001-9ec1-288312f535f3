package com.bxm.customer.domain.vo.valueAdded;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.bxm.file.api.domain.ValueAddedFileDTO;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.time.LocalDate;
import java.util.List;

/**
 * 增值交付单VO
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("增值交付单VO")
public class DeliveryOrderUpsertReq {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", notes = "系统自动生成的唯一标识符")
    private Long id;

    /** 交付单编号（必须，以此为主键） */
    @ApiModelProperty(value = "交付单编号", notes = "可选，系统自动生成")
    private String deliveryOrderNo;

    /** 交付单标题 */
    @Size(max = 500, message = "交付单标题长度不能超过500个字符")
    @ApiModelProperty(value = "交付单标题", notes = "长度不能超过500个字符")
    private String title;

    /** 客户ID */
    //@ApiModelProperty(value = "客户ID")
    //private Long customerId;

    /** 客户企业名称 */
    @NotBlank(message = "客户企业名称不能为空")
    @Size(max = 200, message = "客户企业名称长度不能超过200个字符")
    @ApiModelProperty(value = "客户企业名称", notes = "长度不能超过200个字符", required = true)
    private String customerName;

    /** 统一社会信用代码 */
    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String creditCode;

    /** 税号 */
    @Size(max = 50, message = "税号长度不能超过50个字符")
    @ApiModelProperty(value = "税号", notes = "长度不能超过50个字符")
    private String taxNo;

    /** 纳税性质，1-小规模纳税人，2-一般纳税人 */
    @NotNull(message = "纳税性质不能为空")
    @Min(value = 1, message = "纳税性质必须为1或2")
    @Max(value = 2, message = "纳税性质必须为1或2")
    @ApiModelProperty(value = "纳税性质", notes ="1-小规模纳税人，2-一般纳税人）",required = true, allowableValues = "1,2")
    private Integer taxpayerType;

    /** 增值事项 */
    @NotNull(message = "增值事项不能为空")
    @ApiModelProperty(value = "增值事项", notes = "增值事项类型标识", required = true, allowableValues = "1,2,3,4")
    private Integer valueAddedItemTypeId;

    /** 账期开始时间 (格式：YYYYMM，如：202301) */
    @Min(value = 200001, message = "账期开始时间格式错误，应为YYYYMM且不能早于200001")
    @Max(value = 299912, message = "账期结束时间格式错误，应为YYYYMM且不能晚于299912")
    @ApiModelProperty(value = "账期开始时间", notes = "格式YYYYMM，如：202301")
    private Integer accountingPeriodStart;

    /** 账期结束时间 (格式：YYYYMM，如：202310) */
    @Min(value = 200001, message = "账期结束时间格式错误，应为YYYYMM且不能早于200001")
    @Max(value = 299912, message = "账期结束时间格式错误，应为YYYYMM且不能晚于299912")
    @ApiModelProperty(value = "账期结束时间", notes = "格式YYYYMM，如：202310")
    private Integer accountingPeriodEnd;

    /** 联络人手机号 */
    @Size(max = 20, message = "联络人手机号长度不能超过50个字符")
    @ApiModelProperty(value = "联络人手机号", notes = "长度不能超过20个字符")
    private String contactMobile;

    /** 联络人证件号 */
    @Size(max = 18, message = "联络人证件号长度不能超过18个字符")
    @ApiModelProperty(value = "联络人证件号", notes = "长度不能超过18个字符")
    private String contactIdNumber;

    /** 是否同步汇算，0-否，1-是 */
    @ApiModelProperty(value = "是否同步汇算", notes = "0-否，1-是", allowableValues = "0,1")
    private Boolean syncHandlingFee;

    /** 账务类型信息 */
    @ApiModelProperty(value = "账务类型信息")
    @Valid
    private AccountingInfoVO accountingInfo;

    /** 交付要求 */
    @Size(max = 100, message = "交付要求长度不能超过100个字符")
    @ApiModelProperty(value = "交付要求", notes = "长度不能超过100个字符")
    private String requirements;

    /** 税负要求 */
    @Size(max = 200, message = "税负要求长度不能超过200个字符")
    @ApiModelProperty(value = "税负要求", notes = "长度不能超过200个字符")
    private String taxRequirement;

    /** 是否同步改派，0-否，1-是 */
    @ApiModelProperty(value = "是否同步改派", notes = "0-否，1-是", allowableValues = "0,1")
    private Boolean syncReassignment;

    /** 是否同步联络员，0-否，1-是 */
    @ApiModelProperty(value = "是否同步联络员", notes = "0-否，1-是", allowableValues = "0,1")
    private Boolean syncContactPerson;

    /** 是否同步改账，0-否，1-是 */
    @ApiModelProperty(value = "是否同步改账", notes = "0-否，1-是", allowableValues = "0,1")
    private Boolean syncAccountChange;

    /** 是否修改工期，0-否，1-是 */
    @ApiModelProperty(value = "是否修改工期", notes = "0-否，1-是", allowableValues = "0,1")
    private Boolean modifyDueDate;

    /** 交付截止日期 */
    @ApiModelProperty(value = "交付截止日期", notes = "格式：yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ddl;

    /** 发起部门ID */
    @ApiModelProperty(value = "发起部门ID")
    private Long initiateDeptId;

    /** 会计部门ID */
    @ApiModelProperty(value = "会计部门ID")
    private Long accountingDeptId;

    /** 交付状态 */
    @Size(max = 20, message = "交付状态长度不能超过20个字符")
    @ApiModelProperty(value = "交付状态", notes = "长度不能超过20个字符")
    private String status;

    /** 业务部门id */
    @ApiModelProperty(value = "业务部门ID")
    private Long businessDeptId;

    /** 顶级业务部门id */
    @ApiModelProperty(value = "顶级业务部门ID")
    private Long businessTopDeptId;

    /** 增值事项名称 */
    @ApiModelProperty(value = "增值事项名称")
    private String itemName;


    /** 创建人ID */
    @ApiModelProperty(value = "创建人ID")
    private Long createUid;

    /** 国税账号对象 */
    @Valid
    @ApiModelProperty(value = "国税账号对象", notes = "用于国税账号业务类型的账号信息管理")
    private NationalTaxAccountVO nationalTaxAccount;

    /** 个税账号对象 */
    @Valid
    @ApiModelProperty(value = "个税账号对象", notes = "用于个税账号业务类型的账号信息管理")
    private PersonalTaxAccountVO personalTaxAccount;

    /** 交付文件列表 */
    @ApiModelProperty(value = "交付文件列表", notes = "用于上传交付相关的文件")
    private List<ValueAddedFileDTO> deliveryFiles;

}
