package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 纳税人类型枚举
 * 
 * 用于DeliveryOrderVO中的taxpayerType字段
 * 前端传递code，后端自动转换为description显示
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Getter
@AllArgsConstructor
public enum TaxpayerTypeEnum {

    /**
     * 小规模纳税人
     */
    SMALL_SCALE(1, "小规模纳税人"),

    /**
     * 一般纳税人
     */
    GENERAL(2, "一般纳税人");

    /**
     * 纳税人类型代码
     */
    private final Integer code;

    /**
     * 纳税人类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 纳税人类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TaxpayerTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaxpayerTypeEnum taxpayerType : values()) {
            if (taxpayerType.getCode().equals(code)) {
                return taxpayerType;
            }
        }
        return null;
    }

    /**
     * 验证纳税人类型代码是否有效
     *
     * @param code 纳税人类型代码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 纳税人类型代码
     * @return 纳税人类型描述，如果代码无效则返回null
     */
    public static String getDescriptionByCode(Integer code) {
        TaxpayerTypeEnum taxpayerType = getByCode(code);
        return taxpayerType != null ? taxpayerType.getDescription() : null;
    }
}